package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预算科目信息DTO
 * <AUTHOR>
 * @date 2025-08-04
 */
@Schema(description = "预算科目信息DTO")
@Data
public class BudgetSubjectInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**预算科目编码*/
    @Schema(description = "预算科目编码")
    private String subjectCode;

    /**预算科目名称*/
    @Schema(description = "预算科目名称")
    private String subjectName;

    /**科目释义*/
    @Schema(description = "科目释义")
    private String subjectDescription;

    /**预算金额(万元)*/
    @Schema(description = "预算金额(万元)")
    private BigDecimal budgetAmount;

    /**排序号*/
    @Schema(description = "排序号")
    private Integer sortOrder;
}
