package com.cdkit.modules.cm.api.budget;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.budget.dto.BudgetSubjectInfoDTO;
import com.cdkit.modules.cm.api.budget.dto.CostAnnualBudgetDTO;
import com.cdkit.modules.cm.api.budget.request.CostAnnualBudgetSaveRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 年度总预算API接口
 * <AUTHOR>
 * @date 2025-07-30
 */
@Tag(name = "年度总预算管理")
public interface IAnnualBudgetApi {

    /**
     * 分页查询年度总预算列表
     *
     * @param queryVO 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    @Operation(summary = "年度总预算-分页列表查询")
    @GetMapping("/list")
    Result<IPage<CostAnnualBudgetDTO>> queryPageList(
            CostAnnualBudgetDTO queryVO,
            @Parameter(description = "页码", example = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量", example = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize
    );

    /**
     * 根据ID查询年度总预算详情
     *
     * @param id 年度总预算ID
     * @return 年度总预算详情
     */
    @Operation(summary = "根据ID查询年度总预算详情")
    @GetMapping("/queryById")
    Result<CostAnnualBudgetDTO> queryById(@Parameter(description = "年度总预算ID", required = true) @RequestParam String id);

    /**
     * 编辑年度总预算
     *
     * @param costAnnualBudget 年度总预算数据
     * @return 操作结果
     */
    @Operation(summary = "编辑年度总预算")
    @PutMapping("/edit")
    Result<String> edit(@RequestBody CostAnnualBudgetDTO costAnnualBudget);

    /**
     * 根据ID删除年度总预算
     *
     * @param id 年度总预算ID
     * @return 操作结果
     */
    @Operation(summary = "根据ID删除年度总预算")
    @DeleteMapping("/delete")
    Result<String> delete(@Parameter(description = "年度总预算ID", required = true) @RequestParam String id);

    /**
     * 批量删除年度总预算
     *
     * @param ids 年度总预算ID列表，逗号分隔
     * @return 操作结果
     */
    @Operation(summary = "批量删除年度总预算")
    @DeleteMapping("/deleteBatch")
    Result<String> deleteBatch(@Parameter(description = "年度总预算ID列表，逗号分隔", required = true) @RequestParam String ids);

    /**
     * 生成下一个预算编号
     *
     * @return 下一个预算编号（ZYS+当前年份+3位流水）
     */
    @Operation(summary = "生成下一个预算编号")
    @GetMapping("/generateNextBudgetCode")
    Result<String> generateNextBudgetCode();

    /**
     * 保存年度预算（第一步）
     * 保存年度总预算主表信息、项目年度预算信息和直接成本明细
     *
     * @param request 年度预算保存请求对象
     * @return 操作结果
     */
    @Operation(summary = "保存年度预算（第一步）")
    @PostMapping("/saveStep1")
    Result<String> saveStep1(@RequestBody CostAnnualBudgetSaveRequest request);

    /**
     * 根据项目计划ID查询关联的预算科目信息
     *
     * @param projectPlanId 项目计划ID（可选参数）
     * @return 预算科目信息列表
     */
    @Operation(summary = "根据项目计划ID查询关联的预算科目信息")
    @GetMapping("/subjects")
    Result<List<BudgetSubjectInfoDTO>> queryBudgetSubjects(
            @Parameter(description = "项目计划ID（可选参数）", required = false)
            @RequestParam(name = "projectPlanId", required = false) String projectPlanId);
}
