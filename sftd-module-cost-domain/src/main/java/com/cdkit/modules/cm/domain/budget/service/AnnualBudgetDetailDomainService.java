package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 年度预算明细领域服务
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualBudgetDetailDomainService {

    private final CostAnnualBudgetDetailRepository costAnnualBudgetDetailRepository;

    /**
     * 保存年度预算明细数据
     *
     * @param budgetId 年度预算主表ID
     * @param budgetDetailList 明细数据列表
     */
    public void saveBudgetDetails(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            log.info("没有明细数据需要保存，budgetId: {}", budgetId);
            return;
        }

        log.info("开始保存年度预算明细数据，budgetId: {}, 明细数量: {}", budgetId, budgetDetailList.size());

        // 验证所有明细数据
        for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
            validateBudgetDetail(detailInfo);
            log.info("验证明细数据：项目编号={}, 项目名称={}, 直接成本明细数量={}",
                    detailInfo.getProjectCode(),
                    detailInfo.getProjectName(),
                    detailInfo.getDirectCostList() != null ? detailInfo.getDirectCostList().size() : 0);
        }

        // 通过仓储接口保存明细数据
        costAnnualBudgetDetailRepository.saveBudgetDetails(budgetId, budgetDetailList);

        log.info("年度预算明细数据保存完成，budgetId: {}", budgetId);
    }

    /**
     * 验证明细数据
     */
    private void validateBudgetDetail(CostAnnualBudgetEntity.BudgetDetailInfo detailInfo) {
        if (detailInfo == null) {
            throw new IllegalArgumentException("明细数据不能为空");
        }
        
        if (detailInfo.getProjectCode() == null || detailInfo.getProjectCode().trim().isEmpty()) {
            throw new IllegalArgumentException("项目编号不能为空");
        }
        
        if (detailInfo.getProjectName() == null || detailInfo.getProjectName().trim().isEmpty()) {
            throw new IllegalArgumentException("项目名称不能为空");
        }
        
        // 验证直接成本明细
        if (detailInfo.getDirectCostList() != null) {
            for (CostAnnualBudgetEntity.DirectCostInfo directCostInfo : detailInfo.getDirectCostList()) {
                validateDirectCostInfo(directCostInfo);
            }
        }
    }

    /**
     * 验证直接成本明细数据
     */
    private void validateDirectCostInfo(CostAnnualBudgetEntity.DirectCostInfo directCostInfo) {
        if (directCostInfo == null) {
            throw new IllegalArgumentException("直接成本明细数据不能为空");
        }
        
        if (directCostInfo.getCostItem() == null || directCostInfo.getCostItem().trim().isEmpty()) {
            throw new IllegalArgumentException("费用科目不能为空");
        }
        
        if (directCostInfo.getCostAmount() == null) {
            throw new IllegalArgumentException("支出预算金额不能为空");
        }
        
        if (directCostInfo.getCostAmount().compareTo(java.math.BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("支出预算金额不能为负数");
        }
    }
}
